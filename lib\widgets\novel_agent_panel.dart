import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../models/novel.dart';
import 'novel_agent_diff_view.dart';
import 'novel_agent_message_bubble.dart';

/// 小说 Agent 面板
/// 类似 Cursor IDE 的右侧聊天面板
class NovelAgentPanel extends StatefulWidget {
  final Novel novel;
  final double width;
  
  const NovelAgentPanel({
    super.key,
    required this.novel,
    this.width = 400,
  });

  @override
  State<NovelAgentPanel> createState() => _NovelAgentPanelState();
}

class _NovelAgentPanelState extends State<NovelAgentPanel> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // 章节选择状态
  final Set<int> _selectedChapters = <int>{};

  // 聊天区域高度
  double _chatAreaHeight = 400;
  
  @override
  void initState() {
    super.initState();
    _initializeAgent();
  }

  void _initializeAgent() async {
    await _agentController.setCurrentNovel(widget.novel);
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Obx(() => _agentController.showHistorySidebar.value
          ? Row(
              children: [
                // 历史记录侧边栏
                Container(
                  width: 250,
                  child: _buildHistorySidebar(),
                ),
                // 分隔线
                Container(
                  width: 1,
                  color: Theme.of(context).dividerColor,
                ),
                // 主聊天区域
                Expanded(
                  child: Column(
                    children: [
                      _buildCompactHeader(),
                      SizedBox(
                        height: _chatAreaHeight,
                        child: _buildChatArea(),
                      ),
                      _buildResizableHandle(),
                      _buildInputArea(),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              children: [
                _buildCompactHeader(),
                SizedBox(
                  height: _chatAreaHeight,
                  child: _buildChatArea(),
                ),
                _buildResizableHandle(),
                _buildInputArea(),
              ],
            )),
    );
  }

  /// 构建紧凑头部
  Widget _buildCompactHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.05),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_fix_high,
            color: Theme.of(context).primaryColor,
            size: 18,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              '岱宗AI辅助助手',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          // 历史记录按钮
          Obx(() => IconButton(
            onPressed: _agentController.toggleHistorySidebar,
            icon: Icon(
              _agentController.showHistorySidebar.value
                  ? Icons.history_toggle_off
                  : Icons.history,
              size: 16,
            ),
            tooltip: '历史记录',
            style: IconButton.styleFrom(
              padding: const EdgeInsets.all(4),
              minimumSize: const Size(24, 24),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          )),
          // 模式切换按钮
          Obx(() => IconButton(
            onPressed: _agentController.toggleAgentMode,
            icon: Icon(
              _agentController.isAgentMode.value
                  ? Icons.edit
                  : Icons.chat,
              size: 16,
            ),
            tooltip: _agentController.isAgentMode.value ? '创作模式' : '聊天模式',
            style: IconButton.styleFrom(
              padding: const EdgeInsets.all(4),
              minimumSize: const Size(24, 24),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              backgroundColor: _agentController.isAgentMode.value
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : null,
            ),
          )),
          Text(
            '《${widget.novel.title}》',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }



  /// 构建可拖拽的手柄（用于调整聊天区域高度）
  Widget _buildResizableHandle() {
    return MouseRegion(
      cursor: SystemMouseCursors.resizeRow,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _chatAreaHeight = (_chatAreaHeight + details.delta.dy).clamp(200.0, 600.0);
          });
        },
        child: Container(
          height: 8,
          color: Colors.transparent,
          child: Center(
            child: Container(
              height: 2,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建聊天区域
  Widget _buildChatArea() {
    return Obx(() {
      final messages = _agentController.messages;
      final pendingEdits = _agentController.pendingEdits;
      
      return Column(
        children: [
          // 聊天消息区域
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: messages.length,
              itemBuilder: (context, index) {
                final message = messages[index];
                return NovelAgentMessageBubble(
                  message: message,
                  onApplyEdit: (suggestion) => _agentController.applyEditSuggestion(suggestion),
                  onRejectEdit: (suggestion) => _agentController.rejectEditSuggestion(suggestion),
                );
              },
            ),
          ),
          
          // 待处理编辑区域
          if (pendingEdits.isNotEmpty) _buildPendingEditsArea(),
        ],
      );
    });
  }

  /// 构建待处理编辑区域
  Widget _buildPendingEditsArea() {
    return Obx(() => Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  Icons.pending_actions,
                  color: Colors.orange[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '待处理编辑 (${_agentController.pendingEdits.length})',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _showAllEditsDialog,
                  child: const Text('查看全部'),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ...(_agentController.pendingEdits.take(3).map((edit) => 
            NovelAgentDiffView(
              suggestion: edit,
              onApply: () => _agentController.applyEditSuggestion(edit),
              onReject: () => _agentController.rejectEditSuggestion(edit),
              isCompact: true,
            ),
          )),
          if (_agentController.pendingEdits.length > 3)
            Padding(
              padding: const EdgeInsets.all(8),
              child: Center(
                child: Text(
                  '还有 ${_agentController.pendingEdits.length - 3} 个编辑建议...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ),
            ),
        ],
      ),
    ));
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Obx(() => Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 快捷建议按钮
          if (_agentController.isAgentMode.value) _buildQuickSuggestions(),

          if (_agentController.isAgentMode.value) const SizedBox(height: 8),

          // 输入框行
          Row(
            children: [
              // @按钮
              IconButton(
                onPressed: _showChapterSelector,
                icon: const Icon(Icons.alternate_email),
                iconSize: 20,
                tooltip: '引用章节',
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                  foregroundColor: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 8),

              // 输入框
              Expanded(
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: _agentController.isAgentMode.value
                        ? '输入编辑指令，如"优化对话"、"增加环境描写"等...'
                        : '询问岱宗AI助手...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    isDense: true,
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),

              // 发送按钮
              IconButton(
                onPressed: _agentController.isLoading.value ? null : _sendMessage,
                icon: _agentController.isLoading.value
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.send),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 模式切换按钮（移到输入框下方）
          _buildCompactModeToggle(),

          // 错误提示
          if (_agentController.currentError.value.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                _agentController.currentError.value,
                style: TextStyle(
                  color: Colors.red[600],
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    ));
  }

  /// 构建紧凑的模式切换
  Widget _buildCompactModeToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeButton('聊天模式', false),
          _buildModeButton('创作模式', true),
        ],
      ),
    );
  }

  /// 构建模式按钮
  Widget _buildModeButton(String label, bool isAgentMode) {
    final isSelected = _agentController.isAgentMode.value == isAgentMode;

    return GestureDetector(
      onTap: () => _agentController.setAgentMode(isAgentMode),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected
                ? Colors.white
                : Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  /// 显示章节选择器
  void _showChapterSelector() {
    final chapters = _agentController.availableChapters;
    if (chapters.isEmpty) {
      Get.snackbar('提示', '当前小说没有章节');
      return;
    }

    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          height: 500,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.book, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '选择要引用的章节',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: _buildChapterList(chapters),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _insertSelectedChapters(),
                      child: const Text('插入选中章节'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建章节列表
  Widget _buildChapterList(List<Chapter> chapters) {
    return ListView.builder(
      itemCount: chapters.length,
      itemBuilder: (context, index) {
        final chapter = chapters[index];
        final isSelected = _selectedChapters.contains(chapter.number);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: CheckboxListTile(
            value: isSelected,
            onChanged: (bool? value) {
              setState(() {
                if (value == true) {
                  _selectedChapters.add(chapter.number);
                } else {
                  _selectedChapters.remove(chapter.number);
                }
              });
            },
            title: Text(
              '第${chapter.number}章',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (chapter.title.isNotEmpty)
                  Text(
                    chapter.title,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                Text(
                  '${chapter.content.length}字',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
            secondary: Icon(
              Icons.book_outlined,
              color: Theme.of(context).primaryColor,
            ),
          ),
        );
      },
    );
  }

  /// 插入选中的章节
  void _insertSelectedChapters() {
    if (_selectedChapters.isEmpty) {
      Get.snackbar('提示', '请选择要引用的章节');
      return;
    }

    final sortedChapters = _selectedChapters.toList()..sort();
    final chapterRefs = sortedChapters.map((num) => '@第${num}章').join(' ');

    // 插入到输入框当前光标位置
    final currentText = _messageController.text;
    final selection = _messageController.selection;

    String newText;
    if (selection.isValid) {
      newText = currentText.replaceRange(
        selection.start,
        selection.end,
        chapterRefs,
      );
    } else {
      newText = currentText + chapterRefs;
    }

    _messageController.text = newText;
    _messageController.selection = TextSelection.fromPosition(
      TextPosition(offset: newText.length),
    );

    // 清空选择并关闭对话框
    _selectedChapters.clear();
    Get.back();

    Get.snackbar(
      '成功',
      '已插入 ${sortedChapters.length} 个章节引用',
      duration: const Duration(seconds: 2),
    );
  }

  /// 构建快捷建议
  Widget _buildQuickSuggestions() {
    final suggestions = [
      '优化对话',
      '增加描写',
      '调整节奏',
      '完善情节',
    ];
    
    return Wrap(
      spacing: 8,
      children: suggestions.map((suggestion) => 
        ActionChip(
          label: Text(
            suggestion,
            style: const TextStyle(fontSize: 12),
          ),
          onPressed: () {
            _messageController.text = suggestion;
            _sendMessage();
          },
        ),
      ).toList(),
    );
  }

  /// 发送消息
  void _sendMessage() {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;
    
    _messageController.clear();
    _agentController.sendMessage(content);
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 显示所有编辑对话框
  void _showAllEditsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  const Text(
                    '所有编辑建议',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: Obx(() => ListView.builder(
                  itemCount: _agentController.pendingEdits.length,
                  itemBuilder: (context, index) {
                    final edit = _agentController.pendingEdits[index];
                    return NovelAgentDiffView(
                      suggestion: edit,
                      onApply: () {
                        _agentController.applyEditSuggestion(edit);
                        if (_agentController.pendingEdits.isEmpty) {
                          Navigator.pop(context);
                        }
                      },
                      onReject: () {
                        _agentController.rejectEditSuggestion(edit);
                        if (_agentController.pendingEdits.isEmpty) {
                          Navigator.pop(context);
                        }
                      },
                    );
                  },
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建历史记录侧边栏
  Widget _buildHistorySidebar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // 侧边栏头部
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.05),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '历史记录',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                // 新建会话按钮
                PopupMenuButton<AgentSessionType>(
                  icon: Icon(
                    Icons.add,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  tooltip: '新建会话',
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: AgentSessionType.chat,
                      child: Row(
                        children: [
                          Icon(Icons.chat, size: 16),
                          SizedBox(width: 8),
                          Text('聊天模式'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: AgentSessionType.creative,
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('创作模式'),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (type) {
                    _agentController.createNewSession(type);
                  },
                ),
              ],
            ),
          ),
          // 会话列表
          Expanded(
            child: _buildSessionList(),
          ),
        ],
      ),
    );
  }

  /// 构建会话列表
  Widget _buildSessionList() {
    return Obx(() {
      final sessions = _agentController.sessions
          .where((session) => session.novelTitle == widget.novel.title)
          .toList();

      if (sessions.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '暂无历史记录',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '开始与岱宗AI对话吧',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: sessions.length,
        itemBuilder: (context, index) {
          final session = sessions[index];
          final isActive = _agentController.currentSession.value?.id == session.id;

          return Container(
            margin: const EdgeInsets.only(bottom: 4),
            child: Material(
              color: isActive
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                onTap: () => _agentController.switchToSession(session.id),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            session.typeIcon,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              session.displayTitle,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                                color: isActive
                                    ? Theme.of(context).primaryColor
                                    : null,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (session.summary != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          session.summary!,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      const SizedBox(height: 4),
                      Text(
                        _formatSessionTime(session.lastUpdatedAt),
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }

  /// 格式化会话时间
  String _formatSessionTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}/${time.day}';
    }
  }
}
