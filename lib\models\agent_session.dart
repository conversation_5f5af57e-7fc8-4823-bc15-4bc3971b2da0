import 'package:hive/hive.dart';

part 'agent_session.g.dart';

/// Agent会话类型
@HiveType(typeId: 14)
enum AgentSessionType {
  @HiveField(0)
  chat, // 聊天模式
  @HiveField(1)
  creative, // 创作模式
}

/// Agent会话模型
@HiveType(typeId: 15)
class AgentSession {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final DateTime createdAt;

  @HiveField(3)
  DateTime lastUpdatedAt;

  @HiveField(4)
  final AgentSessionType type;

  @HiveField(5)
  final String novelTitle; // 关联的小说标题

  @HiveField(6)
  String? summary; // 会话摘要

  @HiveField(7)
  final int? chapterNumber; // 关联的章节编号（可选）

  @HiveField(8)
  bool isActive; // 是否为活跃会话

  AgentSession({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.lastUpdatedAt,
    required this.type,
    required this.novelTitle,
    this.summary,
    this.chapterNumber,
    this.isActive = false,
  });

  /// 创建聊天模式会话
  static AgentSession createChat({
    required String novelTitle,
    int? chapterNumber,
    String? customTitle,
  }) {
    final now = DateTime.now();
    final title = customTitle ?? 
        (chapterNumber != null 
            ? '《$novelTitle》第${chapterNumber}章 - 聊天'
            : '《$novelTitle》- 聊天');
    
    return AgentSession(
      id: 'agent_chat_${now.millisecondsSinceEpoch}',
      title: title,
      createdAt: now,
      lastUpdatedAt: now,
      type: AgentSessionType.chat,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      summary: '与岱宗AI辅助助手的对话',
    );
  }

  /// 创建创作模式会话
  static AgentSession createCreative({
    required String novelTitle,
    int? chapterNumber,
    String? customTitle,
  }) {
    final now = DateTime.now();
    final title = customTitle ?? 
        (chapterNumber != null 
            ? '《$novelTitle》第${chapterNumber}章 - 创作'
            : '《$novelTitle》- 创作');
    
    return AgentSession(
      id: 'agent_creative_${now.millisecondsSinceEpoch}',
      title: title,
      createdAt: now,
      lastUpdatedAt: now,
      type: AgentSessionType.creative,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      summary: '与岱宗AI辅助助手的创作协作',
    );
  }

  /// 更新最后更新时间
  void updateLastUpdatedAt() {
    lastUpdatedAt = DateTime.now();
  }

  /// 设置为活跃会话
  void setActive(bool active) {
    isActive = active;
    if (active) {
      updateLastUpdatedAt();
    }
  }

  /// 更新摘要
  void updateSummary(String newSummary) {
    summary = newSummary;
    updateLastUpdatedAt();
  }

  /// 获取显示标题
  String get displayTitle {
    if (chapterNumber != null) {
      return '第${chapterNumber}章 - ${type == AgentSessionType.chat ? '聊天' : '创作'}';
    }
    return type == AgentSessionType.chat ? '聊天模式' : '创作模式';
  }

  /// 获取类型图标
  String get typeIcon {
    switch (type) {
      case AgentSessionType.chat:
        return '💬';
      case AgentSessionType.creative:
        return '✍️';
    }
  }

  /// 转换为Map用于存储
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdatedAt': lastUpdatedAt.millisecondsSinceEpoch,
      'type': type.index,
      'novelTitle': novelTitle,
      'summary': summary,
      'chapterNumber': chapterNumber,
      'isActive': isActive,
    };
  }

  /// 从Map创建对象
  static AgentSession fromMap(Map<String, dynamic> map) {
    return AgentSession(
      id: map['id'] as String,
      title: map['title'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int),
      lastUpdatedAt: DateTime.fromMillisecondsSinceEpoch(map['lastUpdatedAt'] as int),
      type: AgentSessionType.values[map['type'] as int],
      novelTitle: map['novelTitle'] as String,
      summary: map['summary'] as String?,
      chapterNumber: map['chapterNumber'] as int?,
      isActive: map['isActive'] as bool? ?? false,
    );
  }
}
