import 'package:hive/hive.dart';

part 'agent_message.g.dart';

/// Agent消息类型
@HiveType(typeId: 12)
enum AgentMessageType {
  @HiveField(0)
  user,
  @HiveField(1)
  agent,
  @HiveField(2)
  system,
  @HiveField(3)
  error,
}

/// Agent消息模型
@HiveType(typeId: 13)
class AgentMessage {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String content;

  @HiveField(2)
  final AgentMessageType type;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final String? sessionId;

  @HiveField(5)
  final String? novelTitle;

  @HiveField(6)
  final int? chapterNumber;

  @HiveField(7)
  final List<String>? editSuggestionIds; // 存储编辑建议的ID列表

  AgentMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.sessionId,
    this.novelTitle,
    this.chapterNumber,
    this.editSuggestionIds,
  });

  /// 创建用户消息
  static AgentMessage user({
    required String content,
    String? sessionId,
    String? novelTitle,
    int? chapterNumber,
  }) {
    return AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.user,
      timestamp: DateTime.now(),
      sessionId: sessionId,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
    );
  }

  /// 创建Agent消息
  static AgentMessage agent({
    required String content,
    String? sessionId,
    String? novelTitle,
    int? chapterNumber,
    List<String>? editSuggestionIds,
  }) {
    return AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.agent,
      timestamp: DateTime.now(),
      sessionId: sessionId,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      editSuggestionIds: editSuggestionIds,
    );
  }

  /// 创建系统消息
  static AgentMessage system({
    required String content,
    String? sessionId,
    String? novelTitle,
  }) {
    return AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.system,
      timestamp: DateTime.now(),
      sessionId: sessionId,
      novelTitle: novelTitle,
    );
  }

  /// 创建错误消息
  static AgentMessage error({
    required String content,
    String? sessionId,
    String? novelTitle,
  }) {
    return AgentMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: AgentMessageType.error,
      timestamp: DateTime.now(),
      sessionId: sessionId,
      novelTitle: novelTitle,
    );
  }

  /// 转换为Map用于存储
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'type': type.index,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'sessionId': sessionId,
      'novelTitle': novelTitle,
      'chapterNumber': chapterNumber,
      'editSuggestionIds': editSuggestionIds,
    };
  }

  /// 从Map创建对象
  static AgentMessage fromMap(Map<String, dynamic> map) {
    return AgentMessage(
      id: map['id'] as String,
      content: map['content'] as String,
      type: AgentMessageType.values[map['type'] as int],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] as int),
      sessionId: map['sessionId'] as String?,
      novelTitle: map['novelTitle'] as String?,
      chapterNumber: map['chapterNumber'] as int?,
      editSuggestionIds: (map['editSuggestionIds'] as List<dynamic>?)?.cast<String>(),
    );
  }
}
